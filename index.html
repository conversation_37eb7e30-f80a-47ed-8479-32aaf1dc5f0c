<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粒子龙卷风效果</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
        }
        
        #numbers {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            grid-template-rows: repeat(50, 1fr);
            gap: 2px;
            padding: 10px;
            box-sizing: border-box;
            z-index: 0;
            font-size: 12px;
            text-align: center;
        }
        
        .number {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .number.active {
            color: #fff;
            text-shadow: 0 0 10px #00ff00;
        }
        
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="instructions">
        按 Enter 键开始/停止粒子龙卷风效果
    </div>
    
    <div id="numbers"></div>
    <canvas id="canvas"></canvas>

    <script>
        // 初始化数字显示
        const numbersContainer = document.getElementById('numbers');
        for (let i = 1; i <= 1000; i++) {
            const numberDiv = document.createElement('div');
            numberDiv.className = 'number';
            numberDiv.textContent = i.toString().padStart(4, '0');
            numbersContainer.appendChild(numberDiv);
        }

        // Canvas 设置
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 粒子系统
        class Particle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = 0;
                this.vy = 0;
                this.life = 1;
                this.decay = Math.random() * 0.02 + 0.005;
                this.size = Math.random() * 3 + 1;
                this.color = `hsl(${Math.random() * 60 + 180}, 100%, 50%)`;
            }
            
            update(centerX, centerY, time) {
                // 计算到中心的距离和角度
                const dx = this.x - centerX;
                const dy = this.y - centerY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx);
                
                // 龙卷风效果：螺旋向上运动
                const spiralForce = 0.02;
                const upwardForce = 0.5;
                const rotationSpeed = 0.05;
                
                // 螺旋运动
                this.vx += Math.cos(angle + time * rotationSpeed) * spiralForce;
                this.vy += Math.sin(angle + time * rotationSpeed) * spiralForce - upwardForce;
                
                // 向中心的吸引力
                if (distance > 50) {
                    this.vx -= dx * 0.001;
                    this.vy -= dy * 0.001;
                }
                
                // 更新位置
                this.x += this.vx;
                this.y += this.vy;
                
                // 生命周期
                this.life -= this.decay;
                
                // 边界检查，重新生成粒子
                if (this.y < -50 || this.life <= 0) {
                    this.reset(centerX, centerY);
                }
            }
            
            reset(centerX, centerY) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 200 + 100;
                this.x = centerX + Math.cos(angle) * radius;
                this.y = canvas.height + Math.random() * 100;
                this.vx = 0;
                this.vy = 0;
                this.life = 1;
                this.size = Math.random() * 3 + 1;
                this.color = `hsl(${Math.random() * 60 + 180}, 100%, 50%)`;
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.shadowBlur = 10;
                ctx.shadowColor = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 粒子系统管理
        let particles = [];
        let isAnimating = false;
        let animationId;
        let startTime = 0;
        
        function initParticles() {
            particles = [];
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            for (let i = 0; i < 200; i++) {
                particles.push(new Particle(centerX, centerY));
            }
        }
        
        function animate() {
            if (!isAnimating) return;
            
            const currentTime = Date.now();
            const time = (currentTime - startTime) * 0.001;
            
            // 清除画布
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 更新和绘制粒子
            particles.forEach(particle => {
                particle.update(centerX, centerY, time);
                particle.draw();
            });
            
            // 随机激活数字
            const numbers = document.querySelectorAll('.number');
            numbers.forEach(num => num.classList.remove('active'));
            
            for (let i = 0; i < 10; i++) {
                const randomIndex = Math.floor(Math.random() * numbers.length);
                numbers[randomIndex].classList.add('active');
            }
            
            animationId = requestAnimationFrame(animate);
        }
        
        function startTornado() {
            if (isAnimating) return;
            
            isAnimating = true;
            startTime = Date.now();
            initParticles();
            animate();
        }
        
        function stopTornado() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 重置数字状态
            const numbers = document.querySelectorAll('.number');
            numbers.forEach(num => num.classList.remove('active'));
        }
        
        // 键盘事件监听
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                if (isAnimating) {
                    stopTornado();
                } else {
                    startTornado();
                }
            }
        });
    </script>
</body>
</html>
